<template>
  <div class="speech-recognition-demo">
    <h3>语音识别服务切换演示</h3>
    
    <!-- 服务类型选择 -->
    <div class="service-selector">
      <label>
        <input 
          type="radio" 
          value="custom" 
          v-model="selectedType"
          @change="onTypeChange"
        />
        自定义语音识别服务
      </label>
      <label>
        <input 
          type="radio" 
          value="aliyun" 
          v-model="selectedType"
          @change="onTypeChange"
        />
        阿里云语音识别服务
      </label>
    </div>

    <!-- 当前配置信息 -->
    <div class="config-info">
      <h4>当前配置:</h4>
      <p><strong>服务类型:</strong> {{ currentConfig.type }}</p>
      <p v-if="currentConfig.type === 'custom'">
        <strong>WebSocket URL:</strong> {{ currentConfig.wsUrl }}
      </p>
      <p><strong>可用性:</strong> 
        <span :class="{ available: isAvailable, unavailable: !isAvailable }">
          {{ isAvailable ? '可用' : '不可用' }}
        </span>
      </p>
    </div>

    <!-- 测试按钮 -->
    <div class="test-controls">
      <button 
        @click="testConnection" 
        :disabled="!isAvailable || isConnecting"
        class="test-btn"
      >
        {{ isConnecting ? '连接中...' : '测试连接' }}
      </button>
      <button 
        @click="resetTest" 
        :disabled="isConnecting"
        class="reset-btn"
      >
        重置
      </button>
    </div>

    <!-- 测试结果 -->
    <div class="test-results" v-if="testResult">
      <h4>测试结果:</h4>
      <div :class="['result', testResult.success ? 'success' : 'error']">
        <p><strong>状态:</strong> {{ testResult.success ? '成功' : '失败' }}</p>
        <p><strong>消息:</strong> {{ testResult.message }}</p>
        <p v-if="testResult.details"><strong>详情:</strong> {{ testResult.details }}</p>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="usage-info">
      <h4>使用说明:</h4>
      <ul>
        <li>选择不同的语音识别服务类型</li>
        <li>点击"测试连接"验证服务是否可用</li>
        <li>自定义服务使用WebSocket连接到您的语音识别服务器</li>
        <li>阿里云服务需要有效的Token和AppKey</li>
        <li>配置可以在 <code>src/constants/constant.ts</code> 中修改</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { SpeechRecognitionFactory, type ISpeechRecognition } from '@/utils/speechRecognitionFactory';
import { SPEECH_RECOGNITION_CONFIG, type SpeechRecognitionType } from '@/constants/constant';

// 响应式数据
const selectedType = ref<SpeechRecognitionType>(SPEECH_RECOGNITION_CONFIG.TYPE);
const isConnecting = ref(false);
const testResult = ref<{
  success: boolean;
  message: string;
  details?: string;
} | null>(null);

// 计算属性
const currentConfig = computed(() => ({
  type: selectedType.value,
  wsUrl: selectedType.value === 'custom' ? SPEECH_RECOGNITION_CONFIG.CUSTOM.WS_URL : undefined
}));

const isAvailable = computed(() => {
  return SpeechRecognitionFactory.isTypeAvailable(selectedType.value);
});

// 方法
const onTypeChange = () => {
  testResult.value = null;
  console.log(`[Demo] 切换到语音识别服务: ${selectedType.value}`);
};

const testConnection = async () => {
  isConnecting.value = true;
  testResult.value = null;

  try {
    console.log(`[Demo] 开始测试 ${selectedType.value} 语音识别服务`);
    
    // 创建语音识别实例
    const speechRecognition: ISpeechRecognition = SpeechRecognitionFactory.createSpeechRecognition(selectedType.value);
    
    // 初始化配置
    const config = selectedType.value === 'custom' 
      ? { wsUrl: SPEECH_RECOGNITION_CONFIG.CUSTOM.WS_URL }
      : { appKey: 'test-key', token: 'test-token' }; // 测试用的假数据
    
    await speechRecognition.initialize(config, {
      onConnect: () => {
        console.log('[Demo] 连接成功');
        testResult.value = {
          success: true,
          message: '连接成功',
          details: `成功连接到 ${selectedType.value} 语音识别服务`
        };
      },
      onError: (error) => {
        console.error('[Demo] 连接错误:', error);
        testResult.value = {
          success: false,
          message: '连接失败',
          details: error.message
        };
      },
      onDisconnect: (code, reason) => {
        console.log('[Demo] 连接断开:', code, reason);
      }
    });

    // 尝试连接
    await speechRecognition.connect();
    
    // 等待一段时间后断开连接
    setTimeout(() => {
      speechRecognition.disconnect();
    }, 2000);

  } catch (error: any) {
    console.error('[Demo] 测试失败:', error);
    testResult.value = {
      success: false,
      message: '测试失败',
      details: error.message || '未知错误'
    };
  } finally {
    isConnecting.value = false;
  }
};

const resetTest = () => {
  testResult.value = null;
  console.log('[Demo] 重置测试结果');
};

onMounted(() => {
  console.log('[Demo] 语音识别演示组件已加载');
  console.log('[Demo] 当前配置:', SPEECH_RECOGNITION_CONFIG);
});
</script>

<style scoped>
.speech-recognition-demo {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: Arial, sans-serif;
}

.service-selector {
  margin: 15px 0;
}

.service-selector label {
  display: block;
  margin: 8px 0;
  cursor: pointer;
}

.service-selector input[type="radio"] {
  margin-right: 8px;
}

.config-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.config-info h4 {
  margin-top: 0;
}

.available {
  color: #28a745;
  font-weight: bold;
}

.unavailable {
  color: #dc3545;
  font-weight: bold;
}

.test-controls {
  margin: 15px 0;
}

.test-btn, .reset-btn {
  padding: 10px 20px;
  margin-right: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn {
  background: #007bff;
  color: white;
}

.test-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.test-results {
  margin: 15px 0;
}

.result {
  padding: 15px;
  border-radius: 4px;
  margin: 10px 0;
}

.result.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.result.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.usage-info {
  background: #e9ecef;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;
}

.usage-info h4 {
  margin-top: 0;
}

.usage-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.usage-info code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}
</style>
