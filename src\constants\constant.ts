import AiAssistantIcon from "@/components/icons/AiAssistantIcon.vue";
import DiagnosisIcon from "@/components/icons/DiagnosisIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import VoiceRecordingIcon from "@/components/icons/VoiceRecordingIcon.vue";

export type FunctionItem = {
  id: string;
  title: string;
  icon: any;
  subTitle: string;
  description: string;
};

export const FUNCTION_LIST: FunctionItem[] = [
  {
    id: "chat",
    title: "辅助鉴别诊断",
    icon: DiagnosisIcon,
    subTitle: "好兽医AI助手",
    description:
      "无论是宠物健康咨询、医疗建议还是闲聊解压，都可以随时告诉我哦！今天有什么可以帮你的吗？",
  },
  {
    id: "knowledge",
    title: "AI知识助手",
    icon: AiAssistantIcon,
    subTitle: "AI知识助手",
    description:
      "无论是疾病查询、用药指导，还是健康科普，我都在行哦！今天想了解什么医疗知识呢？",
  },
  {
    id: "voice",
    title: "语音病历录入",
    icon: VoiceRecordingIcon,
    subTitle: "语音病历录入助手",
    description:
      "只需说出症状或检查结果，我将为您快速生成规范病历！现在开始吗？",
  },
  {
    id: "function_4",
    title: "影像诊断助手",
    icon: ImageDiagnosisIcon,
    subTitle: "影像诊断助手",
    description:
      "请上传您的检查资料，我将为您提供详细的解读参考！今天需要帮您分析医学影像报告吗？",
  },
];

// 会话相关常量
export const CONVERSATION_TITLE = "宠物健康助手";
export const CONVERSATION_TYPE = 1;

// 登录相关
export const AREA_CODE = [
  {
    key: "cn",
    value: "+86",
    label: "中国",
  },
  {
    key: "hk",
    value: "+852",
    label: "中国香港",
  },
  {
    key: "am",
    value: "+853",
    label: "中国澳门",
  },
  {
    key: "tw",
    value: "+886",
    label: "中国台湾",
  },
];

// 不喜欢反馈类别
export type DislikeCategory = {
  type: number;
  label: string;
  image: string;
};

export const DISLIKE_CATEGORIES: DislikeCategory[] = [
  { type: 1, label: "有害/不安全", image: "/chat/dislike_type_1.jfif" },
  { type: 2, label: "虚假信息", image: "/chat/dislike_type_2.jpeg" },
  { type: 3, label: "没有帮助", image: "/chat/dislike_type_3.jpeg" },
  { type: 4, label: "其他", image: "/chat/dislike_type_4.jfif" },
];
