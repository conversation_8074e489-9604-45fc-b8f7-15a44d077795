<script lang="ts" setup>
import AlgorithmIcon from "@/components/icons/AlgorithmIcon.vue";
import CustomerServiceIcon from "@/components/icons/CustomerServiceIcon.vue";
import DatabaseIcon from "@/components/icons/DatabaseIcon.vue";
import SecurityIcon from "@/components/icons/SecurityIcon.vue";
import { useRouter } from "vue-router";
const router = useRouter();

const handleGoChatView = () => {
  router.push("/chat");
};
</script>

<template>
  <div class="home-container">
    <div class="home-one">
      <img class="home-one-bg" src="/home/<USER>" />
      <div class="home-one-text">
        <div class="home-one-text-title">好兽医 AI助手</div>
        <div class="home-one-text-subtitle">赋能动物医疗新未来</div>
        <div class="home-one-text-desc">
          <p class="home-one-text-desc-p">好兽医AI助手是一款为动物医院、宠物诊所以及兽医专业人士量身打造的智能助手。</p>
          <p class="home-one-text-desc-p">借助最新的宠物医疗算法和语音识别技术，将繁琐的病历录入、信息整理、病情分析</p>
          <p class="home-one-text-desc-p">与客户沟通流程自动化，让兽医们能够更多地专注于诊疗本身。</p>
          <button class="home-one-text-button" @click="handleGoChatView">立即体验网页版</button>
        </div>
      </div>
    </div>

    <div class="home-two">
      <div class="home-two-text">
        <div class="home-two-text-title">为什么选择我们</div>
        <div class="home-two-text-desc">我们致力于为兽医行业提供最专业、最智能的 AI 助手</div>
      </div>
      <div class="home-two-list">
        <div class="home-two-box">
          <div class="home-two-box-icon">
            <AlgorithmIcon class="home-two-box-icon-svg" />
          </div>
          <div class="home-two-box-title">
            先进算法
          </div>
          <div class="home-two-box-desc">
            采用最新深度学习技术，诊断准确率达 90% 以上
          </div>
        </div>
        <div class="home-two-box">
          <div class="home-two-box-icon">
            <DatabaseIcon class="home-two-box-icon-svg" />
          </div>
          <div class="home-two-box-title">
            海量数据
          </div>
          <div class="home-two-box-desc">
            超千万临床病例数据支持，覆盖上百种疾病类型
          </div>
        </div>
        <div class="home-two-box">
          <div class="home-two-box-icon">
            <SecurityIcon class="home-two-box-icon-svg" />
          </div>
          <div class="home-two-box-title">
            安全可靠
          </div>
          <div class="home-two-box-desc">
            通过 ISO27001 认证，确保数据安全，保护隐私
          </div>
        </div>
        <div class="home-two-box">
          <div class="home-two-box-icon">
            <CustomerServiceIcon class="home-two-box-icon-svg" />
          </div>
          <div class="home-two-box-title">
            专业服务
          </div>
          <div class="home-two-box-desc">
            7×24 小时技术支持，确保系统稳定运行
          </div>
        </div>
      </div>
    </div>

    <div class="home-three">
      <div class="home-three-text-title">产品功能展示</div>
      <div class="home-three-carousel">
        <el-carousel height="200px" motion-blur>
          <el-carousel-item>
            <div class="home-three-carousel-item">
              <div class="home-three-carousel-item-left">
                <div class="home-three-carousel-item-title">AI 辅助诊断</div>
                <div class="home-three-carousel-item-desc">运用先进的深度学习算法，结合海量临床数据，为兽医提供准确的诊断建议和治疗方案。系统可快速分析患病动物的症状特征，提供相似病例参考，帮助医生做出更生的诊断决策。</div>
                <div class="home-three-carousel-item-button-list">
                  <div class="home-three-carousel-item-button">智能诊断</div>
                  <div class="home-three-carousel-item-button">病例分析</div>
                  <div class="home-three-carousel-item-button">治疗建议</div>
                </div>
              </div>
              <div class="home-three-carousel-item-right">

              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="footer-section">

    </div>
  </div>
</template>
<style scoped src="./HomeView.css"></style>